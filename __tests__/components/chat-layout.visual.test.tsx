/**
 * Visual layout test for chat components to verify timestamp and execution steps positioning
 */
import React from 'react'
import { render, screen } from '@testing-library/react'
import { formatChatTimestamp } from '@/lib/utils'

// Mock the ReasoningTimeline component to test layout
jest.mock(
  '@/app/(conv)/dragTree/[dragTreeId]/components/chat/ReasoningTimeline',
  () => {
    return {
      ReasoningTimeline: ({ stepCount }: { stepCount: number }) => (
        <div
          data-testid="reasoning-timeline"
          className="border border-gray-200/60 rounded-md bg-gray-50/50 p-1.5"
        >
          <button className="h-5 px-2 text-xs font-normal text-gray-400">
            {stepCount} steps
          </button>
        </div>
      ),
    }
  }
)

describe('Chat Layout Visual Tests', () => {
  beforeEach(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date('2024-01-15T10:30:00Z'))
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Timestamp Formatting and Positioning', () => {
    it('should format timestamps with proper visual hierarchy', () => {
      const recentTime = new Date('2024-01-15T10:25:00Z') // 5 minutes ago
      const todayTime = new Date('2024-01-15T08:00:00Z') // Earlier today
      const oldTime = new Date('2024-01-08T12:00:00Z') // Last week

      expect(formatChatTimestamp(recentTime)).toBe('5 minutes ago')
      expect(formatChatTimestamp(todayTime)).toMatch(
        /^Today \d{1,2}:\d{2} [AP]M$/
      )
      expect(formatChatTimestamp(oldTime)).toMatch(
        /Jan 8, 2024 \d{1,2}:\d{2} [AP]M/
      )
    })

    it('should render timestamp with subtle styling', () => {
      const TestComponent = () => (
        <div className="relative group">
          <div className="chat-bubble assistant-bubble">
            <div>Test message content</div>
          </div>
          <div className="text-xs text-gray-400 mt-0.5 px-1 text-left ml-2">
            5 minutes ago
          </div>
        </div>
      )

      render(<TestComponent />)

      const timestamp = screen.getByText('5 minutes ago')
      expect(timestamp).toBeInTheDocument()
      expect(timestamp).toHaveClass(
        'text-xs',
        'text-gray-400',
        'mt-0.5',
        'px-1',
        'text-left',
        'ml-2'
      )
    })
  })

  describe('Execution Steps Layout', () => {
    it('should render reasoning timeline with subtle styling', () => {
      const {
        ReasoningTimeline,
      } = require('@/app/(conv)/dragTree/[dragTreeId]/components/chat/ReasoningTimeline')

      render(<ReasoningTimeline stepCount={3} />)

      const timeline = screen.getByTestId('reasoning-timeline')
      expect(timeline).toBeInTheDocument()
      expect(timeline).toHaveClass(
        'border',
        'border-gray-200/60',
        'rounded-md',
        'bg-gray-50/50',
        'p-1.5'
      )

      const stepButton = screen.getByText('3 steps')
      expect(stepButton).toHaveClass(
        'h-5',
        'px-2',
        'text-xs',
        'font-normal',
        'text-gray-400'
      )
    })
  })

  describe('Message Container Integration', () => {
    it('should position elements within message container for cohesive grouping', () => {
      const MessageContainer = () => (
        <div className="mb-4 text-left">
          <div className="relative group">
            {/* Reasoning timeline positioned within message container */}
            <div className="mb-1 flex justify-start">
              <div
                className="text-xs max-w-[80%]"
                data-testid="timeline-container"
              >
                <div data-testid="reasoning-timeline">3 steps</div>
              </div>
            </div>

            {/* Message bubble */}
            <div
              className="chat-bubble assistant-bubble"
              data-testid="message-bubble"
            >
              <div>Assistant message content</div>
            </div>

            {/* Timestamp positioned closer to bubble */}
            <div
              className="text-xs text-gray-400 mt-0.5 px-1 text-left ml-2"
              data-testid="timestamp"
            >
              5 minutes ago
            </div>
          </div>
        </div>
      )

      render(<MessageContainer />)

      // Verify all elements are present and properly positioned
      expect(screen.getByTestId('timeline-container')).toBeInTheDocument()
      expect(screen.getByTestId('message-bubble')).toBeInTheDocument()
      expect(screen.getByTestId('timestamp')).toBeInTheDocument()

      // Verify timeline container has proper constraints
      const timelineContainer = screen.getByTestId('timeline-container')
      expect(timelineContainer).toHaveClass('text-xs', 'max-w-[80%]')

      // Verify timestamp has improved positioning
      const timestamp = screen.getByTestId('timestamp')
      expect(timestamp).toHaveClass('mt-0.5', 'px-1', 'ml-2')
    })
  })

  describe('Copy Button Integration', () => {
    it('should position copy button alongside timestamp for better message grouping', () => {
      const MessageWithCopyButton = () => (
        <div className="relative group">
          <div className="chat-bubble assistant-bubble">
            <div>Assistant message content</div>
          </div>

          {/* Timestamp and copy button row */}
          <div
            className="flex items-center justify-between mt-0.5 px-1 flex-row ml-2"
            data-testid="timestamp-copy-row"
          >
            <div className="text-xs text-gray-400" data-testid="timestamp">
              5 minutes ago
            </div>
            <button
              className="text-xs text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-gray-100"
              data-testid="copy-button"
            >
              Copy
            </button>
          </div>
        </div>
      )

      render(<MessageWithCopyButton />)

      // Verify timestamp and copy button are in the same row
      const timestampCopyRow = screen.getByTestId('timestamp-copy-row')
      expect(timestampCopyRow).toBeInTheDocument()
      expect(timestampCopyRow).toHaveClass(
        'flex',
        'items-center',
        'justify-between'
      )

      // Verify copy button has proper styling for integration
      const copyButton = screen.getByTestId('copy-button')
      expect(copyButton).toHaveClass(
        'opacity-0',
        'group-hover:opacity-100',
        'transition-all'
      )

      // Verify timestamp is still present
      const timestamp = screen.getByTestId('timestamp')
      expect(timestamp).toBeInTheDocument()
      expect(timestamp).toHaveClass('text-xs', 'text-gray-400')
    })
  })

  describe('Visual Hierarchy', () => {
    it('should create proper visual grouping with reduced prominence', () => {
      // Test that step count text is more subtle
      const stepText = 'text-xs font-normal text-gray-400'
      expect(stepText).toContain('font-normal') // Not font-medium
      expect(stepText).toContain('text-gray-400') // More subtle than text-gray-500

      // Test that timestamp is positioned closer to message
      const timestampClasses = 'mt-0.5 px-1' // Reduced margin and padding
      expect(timestampClasses).toContain('mt-0.5') // Less than mt-1

      // Test that container has reduced visual weight
      const containerClasses = 'border-gray-200/60 bg-gray-50/50 p-1.5'
      expect(containerClasses).toContain('/60') // Reduced opacity
      expect(containerClasses).toContain('/50') // Reduced background opacity
      expect(containerClasses).toContain('p-1.5') // Reduced padding
    })
  })
})
