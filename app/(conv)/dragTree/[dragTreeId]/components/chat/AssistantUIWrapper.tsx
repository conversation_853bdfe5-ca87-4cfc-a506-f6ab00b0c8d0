'use client'

import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  createContext,
  useContext,
} from 'react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import {
  AssistantRuntimeProvider,
  MessagePrimitive,
  useMessage,
  ThreadPrimitive,
  ComposerPrimitive,
} from '@assistant-ui/react'
import { ReasoningTimeline } from './ReasoningTimeline'
import {
  FiCopy,
  FiChevronDown,
  FiChevronUp,
  FiSearch,
  FiLoader,
  FiArrowDown,
} from 'react-icons/fi'
import toast from 'react-hot-toast'
import { CONTEXT_CONFIG } from '@/app/api/aipane/assistant/config'
import ReadOnlyTiptapEditor from '@/app/components/editor/ReadOnlyTiptapEditor'
import { formatChatTimestamp } from '@/lib/utils'

// Context to pass initial messages data to custom message components
const InitialMessagesContext = createContext<ExtendedMessage[]>([])
const useInitialMessages = () => useContext(InitialMessagesContext)

/**
 * Extended message type compatible with ChatTabContent
 */
type ExtendedMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: string
  stepCount?: number
  steps?: any[]
  isStreaming?: boolean
  cleanContent?: string
  timestamp?: Date
}

/**
 * Props for AssistantUIWrapper component
 */
type AssistantUIWrapperProps = {
  conversationId?: string
  initialMessages?: ExtendedMessage[]
  onMessageFinish?: (message: any) => void
  // Pagination support
  hasMoreMessages?: boolean
  isLoadingMoreMessages?: boolean
  onLoadMoreMessages?: () => void
  // Real-time step streaming
  liveSteps?: any[]
  isStreamingSteps?: boolean
}

/**
 * Custom message component that can render tool calls and reasoning
 */
const CustomMessage: React.FC<{
  messageId?: string
  conversationId?: string
  liveSteps?: any[]
  isStreamingSteps?: boolean
}> = ({
  messageId,
  conversationId,
  liveSteps = [],
  isStreamingSteps = false,
}) => {
  // Use selector that returns the store object itself to keep snapshot stable
  const content = useMessage(m => m.content)
  const role = useMessage(m => m.role)
  const status = useMessage(m => m.status)
  const id = useMessage(m => m.id)
  const createdAt = useMessage(m => (m as any).createdAt)

  // Get initial messages from context to find step data
  const initialMessages = useInitialMessages()

  // Find step data for this message from initial messages or use live steps if streaming
  const stepData = React.useMemo(() => {
    const messageData = initialMessages.find(
      msg => msg.id === (id || messageId)
    )
    const isCurrentMessage =
      status?.type === 'running' ||
      (messageData?.isStreaming && isStreamingSteps)

    // Use live steps for the currently streaming message, otherwise use stored steps
    if (isCurrentMessage && isStreamingSteps && liveSteps.length > 0) {
      return {
        stepCount: liveSteps.length,
        steps: liveSteps,
        isStreaming: true,
      }
    }

    return {
      stepCount: messageData?.stepCount || 0,
      steps: messageData?.steps || [],
      isStreaming: messageData?.isStreaming || false,
    }
  }, [initialMessages, id, messageId, status, isStreamingSteps, liveSteps])

  // Get timestamp for display - prefer createdAt from useMessage, fallback to initial message data
  const messageData = initialMessages.find(msg => msg.id === (id || messageId))
  const messageTimestamp =
    createdAt || messageData?.timestamp || messageData?.createdAt
  const formattedTimestamp = messageTimestamp
    ? formatChatTimestamp(messageTimestamp)
    : ''

  const message = React.useMemo(
    () => ({
      content,
      role,
      status,
      id: id || messageId || '',
    }),
    [content, role, status, id, messageId]
  )

  // ────────────────────────────────────────────────
  // Tool-call rendering – show live web_search queries
  // ────────────────────────────────────────────────
  const WebSearchToolCallPart: React.FC<any> = ({ args, status }) => {
    const query = args?.query || ''
    const isRunning = status?.type === 'running'
    const resultCount = status?.result?.resultCount || status?.resultCount
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500 my-1">
        <FiSearch className="w-3 h-3" />
        {isRunning ? (
          <>
            <FiLoader className="w-3 h-3 animate-spin" /> Searching:{' '}
            <span className="font-mono">{query}</span>
          </>
        ) : (
          <>
            Found <span className="font-semibold">{resultCount ?? '?'}</span>{' '}
            results for <span className="font-mono">{query}</span>
          </>
        )}
      </div>
    )
  }

  // Memoise tool components mapping to avoid new object each render
  const toolComponents = React.useMemo(
    () => ({
      Fallback: WebSearchToolCallPart,
      by_name: {
        web_search: WebSearchToolCallPart,
      },
    }),
    []
  )
  // Extract plain text and generate editor content for TipTap rendering
  const { editorContent, plainText } = React.useMemo((): {
    editorContent: string
    plainText: string
  } => {
    if (!message.content) return { editorContent: '', plainText: '' }
    if (typeof message.content === 'string')
      return {
        editorContent: message.content as string,
        plainText: message.content as string,
      }
    if (Array.isArray(message.content)) {
      const txt = (message.content as any[])
        .map((p: any) => {
          if (typeof p === 'string') return p
          if (p && p.type === 'text' && typeof p.text === 'string')
            return p.text
          return ''
        })
        .join('')
      return { editorContent: txt, plainText: txt }
    }
    return { editorContent: '', plainText: '' }
  }, [message.content])

  // Determine if message is long (heuristic by character count)
  const LONG_THRESHOLD = 800
  const charCount = plainText.length

  const hasTextContent = plainText.trim().length > 0

  const isLong = charCount > LONG_THRESHOLD
  const [collapsed, setCollapsed] = useState<boolean>(isLong)

  // Check if message is currently streaming
  const isStreaming = useMessage(m => (m as any).isStreaming ?? false)

  // Copy message content to clipboard
  const handleCopy = async () => {
    try {
      const textToCopy =
        typeof message.content === 'string'
          ? (message.content as string)
          : Array.isArray(message.content)
            ? (message.content as any[])
                .map(part => {
                  if (typeof part === 'string') return part
                  // Fallback for structured parts
                  if ('text' in part) return part.text
                  return ''
                })
                .join('')
            : JSON.stringify(message.content)

      await navigator.clipboard.writeText(textToCopy)
      toast.success('Message copied')
    } catch (e) {
      toast.error('Failed to copy')
    }
  }

  // Check if this message has step count (indicating reasoning/tool usage)
  const hasSteps = message.role === 'assistant' && stepData.stepCount > 0

  // Temporary debug logging to verify fix
  if (message.role === 'assistant') {
    console.log('[AssistantUIWrapper] Message step data:', {
      messageId: message.id,
      stepCount: stepData.stepCount,
      hasSteps,
      initialMessagesCount: initialMessages.length,
    })
  }

  const bubbleBaseClasses =
    'relative inline-block rounded-lg max-w-[80%] text-sm leading-relaxed'
  const bubblePadding = 'px-3 py-2'

  const bubbleColorClasses =
    message.role === 'user'
      ? 'bg-blue-600 text-white ml-auto mr-2'
      : 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100 ml-2 mr-auto'

  return (
    <MessagePrimitive.Root>
      <div
        className={`mb-4 ${
          message.role === 'user' ? 'text-right' : 'text-left'
        }`}
      >
        {/* Message container with group hover for copy button */}
        <div className="relative group">
          {/* Show reasoning timeline for assistant messages with steps - positioned within message container */}
          {hasSteps && (
            <div
              className={`mb-1 ${message.role === 'user' ? 'flex justify-end' : 'flex justify-start'}`}
            >
              <div className="text-xs max-w-[80%]">
                <ReasoningTimeline
                  messageId={message.id}
                  stepCount={stepData.stepCount}
                  isStreaming={stepData.isStreaming}
                  liveSteps={stepData.steps}
                />
              </div>
            </div>
          )}

          <div
            className={`chat-bubble ${message.role === 'user' ? 'user-bubble' : 'assistant-bubble'}`}
          >
            {/* Message content with clickable expansion and smooth animations */}
            <div className="whitespace-pre-wrap break-words">
              {!hasTextContent ? (
                <MessagePrimitive.Parts
                  components={{ tools: toolComponents }}
                />
              ) : isStreaming ? (
                <MessagePrimitive.Content
                  components={{ tools: toolComponents }}
                />
              ) : collapsed && isLong ? (
                <>
                  <div
                    className={`message-content collapsed clickable-message rounded-lg p-1 ${message.role === 'assistant' ? 'hover:bg-gray-50' : 'hover:bg-blue-50'}`}
                    onClick={() => setCollapsed(false)}
                    title="Click to expand message"
                  >
                    <ReadOnlyTiptapEditor content={editorContent} />
                  </div>
                  <div className="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-b from-transparent to-inherit pointer-events-none rounded-b-lg" />
                  <button
                    onClick={e => {
                      e.stopPropagation()
                      setCollapsed(false)
                    }}
                    className="expand-button fade-in"
                  >
                    Show more <FiChevronDown className="w-3 h-3" />
                  </button>
                </>
              ) : (
                <>
                  <div className="message-content expanded fade-in">
                    <ReadOnlyTiptapEditor content={editorContent} />
                  </div>
                  {isLong && (
                    <button
                      onClick={() => setCollapsed(true)}
                      className="expand-button"
                    >
                      Show less <FiChevronUp className="w-3 h-3" />
                    </button>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Timestamp display - positioned closer to message bubble */}
          {formattedTimestamp && (
            <div
              className={`text-xs text-gray-400 mt-0.5 px-1 ${
                message.role === 'user' ? 'text-right mr-2' : 'text-left ml-2'
              }`}
            >
              {formattedTimestamp}
            </div>
          )}

          {/* Copy button - positioned appropriately for each message type */}
          <button
            onClick={handleCopy}
            className={`absolute transform text-xs text-gray-500 hover:text-gray-700 opacity-0 group-hover:opacity-100 transition-all duration-200 bg-white/95 backdrop-blur-sm rounded-md p-1.5 shadow-sm border border-gray-200 hover:border-gray-300 hover:shadow-md ${
              message.role === 'user'
                ? 'bottom-0 right-0 translate-x-2 translate-y-2' // User: outside bubble
                : 'bottom-1 right-1' // Assistant: original position inside bubble
            }`}
            title="Copy message"
          >
            <FiCopy className="w-3 h-3" />
          </button>
        </div>
      </div>
    </MessagePrimitive.Root>
  )
}

/**
 * Assistant-UI wrapper component for chat interface
 * Provides a modern, robust chat UI to replace the legacy implementation
 */
export default function AssistantUIWrapper({
  conversationId,
  initialMessages = [],
  onMessageFinish,
  hasMoreMessages = false,
  isLoadingMoreMessages = false,
  onLoadMoreMessages,
  liveSteps = [],
  isStreamingSteps = false,
}: AssistantUIWrapperProps) {
  // Scroll management state
  // ── Input composer state ───────────────────────────────
  const [inputValue, setInputValue] = useState('')
  const isOverLimit = inputValue.length > CONTEXT_CONFIG.MAX_INPUT_CHARS

  const [isAtBottom, setIsAtBottom] = useState(true)
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const isInitialLoad = useRef(true)

  // Convert our message format to assistant-ui format
  const formattedMessages = initialMessages.map(msg => ({
    id: msg.id,
    role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
    content: ('cleanContent' in msg ? msg.cleanContent : null) || msg.content,
    createdAt:
      msg.timestamp ||
      ('createdAt' in msg && msg.createdAt
        ? new Date(msg.createdAt)
        : new Date()),
    // Include step data for reasoning timeline
    stepCount: 'stepCount' in msg ? msg.stepCount : undefined,
    isStreaming: 'isStreaming' in msg ? msg.isStreaming : undefined,
  }))

  // Debug: log initial messages to verify step data
  console.log(
    '[AssistantUIWrapper] Initial messages with step data:',
    initialMessages.map(msg => ({
      id: msg.id,
      role: msg.role,
      stepCount: msg.stepCount,
      hasSteps: !!msg.stepCount && msg.stepCount > 0,
    }))
  )

  // Set up chat runtime with API endpoint
  const runtime = useChatRuntime({
    api: '/api/aipane/chat',
    initialMessages: formattedMessages,
    body: {
      conversationId,
    },
    onFinish: onMessageFinish,
  })

  // Handle scroll events
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const { scrollTop, scrollHeight, clientHeight } = container
    const isNearBottom = scrollHeight - scrollTop - clientHeight <= 50

    setIsAtBottom(isNearBottom)
    setShowScrollToBottom(!isNearBottom && scrollTop > 200)
  }, [])

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      })
    }
  }, [])

  // Auto-scroll to bottom on initial load and new messages
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    let timeoutId: NodeJS.Timeout

    // For initial load, scroll to bottom immediately
    if (isInitialLoad.current) {
      timeoutId = setTimeout(() => {
        container.scrollTop = container.scrollHeight
        isInitialLoad.current = false
      }, 100)
      return () => clearTimeout(timeoutId)
    }

    // For new messages, auto-scroll if user is at bottom
    if (isAtBottom) {
      timeoutId = setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth',
        })
      }, 100)
    }

    // Cleanup timeout on dependency change or unmount
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [formattedMessages.length, isAtBottom])

  // Create a wrapper component that passes live step props
  const MessageWithLiveSteps = React.useCallback(
    (props: any) => {
      return (
        <CustomMessage
          {...props}
          liveSteps={liveSteps}
          isStreamingSteps={isStreamingSteps}
        />
      )
    },
    [liveSteps, isStreamingSteps]
  )

  return (
    <InitialMessagesContext.Provider value={initialMessages}>
      <AssistantRuntimeProvider runtime={runtime}>
        <div className="flex flex-col h-full">
          {/* Main chat thread with custom message renderer and built-in pagination */}
          <div className="flex-1 min-h-0 relative">
            <div
              ref={scrollContainerRef}
              className="h-full overflow-y-auto chat-thread"
              onScroll={handleScroll}
            >
              <ThreadPrimitive.Root className="h-full min-h-0">
                <ThreadPrimitive.Messages
                  components={{ Message: MessageWithLiveSteps }}
                />
              </ThreadPrimitive.Root>
            </div>

            {/* Scroll to bottom button - Small and centered */}
            {showScrollToBottom && (
              <button
                onClick={scrollToBottom}
                className="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-600 hover:bg-gray-700 text-white rounded-full p-2 shadow-md hover:shadow-lg transition-all duration-200 z-10 opacity-80 hover:opacity-100"
                aria-label="Scroll to bottom"
              >
                <FiArrowDown className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Input composer - Compact horizontal layout */}
          <div className="border-t-2 border-gray-200 bg-white/90 backdrop-blur-sm p-3">
            <ComposerPrimitive.Root>
              {/* Textarea and send button in horizontal layout */}
              <div className="flex items-end gap-2">
                <ComposerPrimitive.Input
                  placeholder="Message AI Assistant..."
                  className={`flex-1 resize-y overflow-auto max-h-32 min-h-[36px] rounded-lg border-2 bg-white px-3 py-2 text-sm leading-tight text-gray-900 placeholder-gray-500 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md focus:border-blue-500 focus:shadow-lg focus:ring-2 focus:ring-blue-500/20 ${isOverLimit ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-gray-200'}`}
                  rows={1}
                  value={inputValue}
                  onChange={(e: any) => setInputValue(e.target.value)}
                  onKeyDown={(e: any) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      if (!isOverLimit && inputValue.trim()) {
                        // Trigger send
                        const sendButton = e.target
                          .closest('form')
                          ?.querySelector('[data-send-button]')
                        sendButton?.click()
                      }
                    }
                  }}
                />
                <ComposerPrimitive.Send
                  data-send-button
                  className="w-9 h-9 p-0 rounded-lg transition-all duration-200 flex items-center justify-center bg-emerald-500 hover:bg-emerald-600 text-white shadow-md hover:shadow-lg disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed hover:scale-105 active:scale-95"
                  disabled={isOverLimit || !inputValue.trim()}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                    />
                  </svg>
                </ComposerPrimitive.Send>
              </div>

              {/* Bottom info section - compact */}
              <div className="mt-1 flex justify-between items-center text-xs px-1">
                {isOverLimit ? (
                  /* Show only warning when over limit */
                  <div className="text-red-700 font-medium text-xs">
                    ⚠️ Message too long. Limit: {CONTEXT_CONFIG.MAX_INPUT_CHARS}{' '}
                    chars.
                  </div>
                ) : (
                  /* Show shortcuts when under limit */
                  <span className="text-gray-600">
                    Press Enter to send, Shift+Enter for new line
                  </span>
                )}

                {/* Character counter */}
                <span
                  className={
                    isOverLimit ? 'text-red-600 font-semibold' : 'text-gray-500'
                  }
                >
                  {inputValue.length}/{CONTEXT_CONFIG.MAX_INPUT_CHARS}
                </span>
              </div>
            </ComposerPrimitive.Root>
          </div>
        </div>
      </AssistantRuntimeProvider>
    </InitialMessagesContext.Provider>
  )
}
