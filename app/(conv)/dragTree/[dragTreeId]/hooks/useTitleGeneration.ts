import { useCallback, useRef } from 'react'

type TitleGenerationOptions = {
  conversationId: string
  userPrompt: string
  contextQuestions: string[]
  firstMessage: string
  preferredLanguage?: string
  onTitleGenerated?: (title: string) => void
  onError?: (error: string) => void
}

/**
 * Hook for generating conversation titles using structured output
 * Only generates title once per conversation to avoid duplicates
 */
export function useTitleGeneration() {
  const generatedConversations = useRef<Set<string>>(new Set())

  const generateTitle = useCallback(async (options: TitleGenerationOptions) => {
    const {
      conversationId,
      userPrompt,
      contextQuestions,
      firstMessage,
      preferredLanguage = 'en',
      onTitleGenerated,
      onError,
    } = options

    // Check if we've already generated a title for this conversation
    if (generatedConversations.current.has(conversationId)) {
      console.log(
        '📝 [TitleGeneration] Title already generated for conversation:',
        conversationId
      )
      return
    }

    try {
      console.log(
        '📝 [TitleGeneration] Starting title generation for conversation:',
        conversationId
      )

      const response = await fetch('/api/aipane/generate-title', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversationId,
          userPrompt,
          contextQuestions,
          firstMessage,
          preferredLanguage,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // Read the streaming response
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let generatedTitle = ''
      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value, { stream: true })
          generatedTitle += chunk
        }
      } finally {
        reader.releaseLock()
      }

      // Parse the final title (streamObject returns JSON)
      try {
        const parsed = JSON.parse(generatedTitle)
        const title = parsed.title || generatedTitle.trim()

        if (title) {
          console.log(
            '📝 [TitleGeneration] Title generated successfully:',
            title
          )

          // Mark this conversation as having a generated title
          generatedConversations.current.add(conversationId)

          // Call the success callback
          if (onTitleGenerated) {
            onTitleGenerated(title)
          }
        } else {
          throw new Error('No title in response')
        }
      } catch (parseError) {
        // If JSON parsing fails, use the raw text as title
        const title = generatedTitle.trim()
        if (title) {
          console.log('📝 [TitleGeneration] Using raw title:', title)
          generatedConversations.current.add(conversationId)
          if (onTitleGenerated) {
            onTitleGenerated(title)
          }
        } else {
          throw new Error('Empty title response')
        }
      }
    } catch (error) {
      console.error('📝 [TitleGeneration] Error generating title:', error)

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      if (onError) {
        onError(errorMessage)
      }
    }
  }, [])

  const resetGeneratedConversations = useCallback(() => {
    generatedConversations.current.clear()
  }, [])

  return {
    generateTitle,
    resetGeneratedConversations,
  }
}
