'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'
import {
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ComposerPrimitive,
  MessagePrimitive,
  useMessage,
} from '@assistant-ui/react'
import {
  FiCopy,
  FiChevronDown,
  FiChevronUp,
  FiSearch,
  FiLoader,
  FiArrowDown,
} from 'react-icons/fi'
import ReadOnlyTiptapEditor from '@/app/components/editor/ReadOnlyTiptapEditor'
import toast from 'react-hot-toast'
import { CONTEXT_CONFIG } from '@/app/api/aipane/assistant/config'
import { isFeatureEnabled } from '@/app/configs/features'

/**
 * Props for ChatV2 component
 */
type ChatV2Props = {
  /** Conversation ID (required, must start with 'thread_') */
  conversationId: string
  /** Context document IDs for new conversations */
  contextIds?: string[]
  /** Additional settings */
  settings?: Record<string, unknown>
  /** Callback when message is finished */
  onMessageFinish?: (message: any) => void
  /** Show context picker for new conversations */
  showContextPicker?: boolean
  /** Callback when context changes */
  onContextChange?: (contextIds: string[]) => void
}

/**
 * Custom message component for enhanced display
 */
const CustomMessage: React.FC = () => {
  const content = useMessage(m => m.content)
  const role = useMessage(m => m.role)

  // Determine if message is long
  const LONG_THRESHOLD = 800
  // Extract raw string for editor; keep plainText for length calculation
  const { editorContent, plainText } = React.useMemo((): {
    editorContent: string
    plainText: string
  } => {
    if (!content) return { editorContent: '', plainText: '' }
    if (typeof content === 'string')
      return { editorContent: content as string, plainText: content as string }
    if (Array.isArray(content)) {
      const txt = (content as any[])
        .map((p: any) => {
          if (typeof p === 'string') return p
          if (p && p.type === 'text' && typeof p.text === 'string')
            return p.text
          return ''
        })
        .join('')
      // joined markdown acts as both editor and plain
      return { editorContent: txt, plainText: txt }
    }
    return { editorContent: '', plainText: '' }
  }, [content])

  const charCount = plainText.length
  const isLong = charCount > LONG_THRESHOLD
  const [collapsed, setCollapsed] = useState<boolean>(isLong)

  // Copy message content
  const handleCopy = async () => {
    try {
      const textToCopy =
        typeof content === 'string'
          ? (content as string)
          : JSON.stringify(content)
      await navigator.clipboard.writeText(textToCopy)
      toast.success('Message copied')
    } catch {
      toast.error('Failed to copy')
    }
  }

  // Tool call rendering for web search - only show tool calls, hide results
  const WebSearchToolCallPart: React.FC<any> = ({ args, status }) => {
    const query = args?.query || ''
    const isRunning = status?.type === 'running'

    // Only show tool calls (running state), hide tool results (complete state)
    if (!isRunning && status?.type === 'complete') {
      return null
    }

    return (
      <div className="flex items-center gap-2 text-sm text-blue-600 my-2 p-2 bg-blue-50 rounded-lg border-l-2 border-blue-200">
        <FiSearch className="w-4 h-4 flex-shrink-0" />
        {isRunning ? (
          <div className="flex items-center gap-2">
            <FiLoader className="w-4 h-4 animate-spin" />
            <span>
              Searching web for: <span className="font-medium">{query}</span>
            </span>
          </div>
        ) : (
          <span>
            Searched: <span className="font-medium">{query}</span>
          </span>
        )}
      </div>
    )
  }

  const toolComponents = React.useMemo(
    () => ({
      Fallback: WebSearchToolCallPart,
      by_name: {
        web_search: WebSearchToolCallPart,
      },
    }),
    []
  )

  const hasTextContent = (() => {
    if (!content) return false
    if (typeof content === 'string')
      return (content as string).trim().length > 0
    if (Array.isArray(content)) {
      return (content as any[]).some((p: any) => {
        if (!p || p.type !== 'text') return false
        const txt = p.text as string | undefined
        return typeof txt === 'string' && (txt as string).trim().length > 0
      })
    }
    return false
  })()

  // Determine if message is still streaming (assistant-ui adds isStreaming flag)
  // Fallback: if role === 'assistant' and content ends with '\uFFFD' parts array.
  const isStreaming = useMessage(m => (m as any).isStreaming ?? false)

  if (role === 'system') return null // Don't show system messages

  return (
    <MessagePrimitive.Root>
      <div className={`mb-4 ${role === 'user' ? 'text-right' : 'text-left'}`}>
        <div
          className={`chat-bubble ${role === 'user' ? 'user-bubble' : 'assistant-bubble'} group`}
        >
          {/* Copy button */}
          <button
            onClick={handleCopy}
            className="copy-button"
            title="Copy message"
          >
            <FiCopy className="w-3 h-3" />
          </button>

          {/* Message content */}
          <div className="whitespace-pre-wrap break-words">
            {!hasTextContent ? (
              <MessagePrimitive.Parts components={{ tools: toolComponents }} />
            ) : isStreaming ? (
              // While streaming, show plain content parts for speed
              <MessagePrimitive.Content
                components={{ tools: toolComponents }}
              />
            ) : collapsed && isLong ? (
              <>
                <div className="overflow-hidden max-h-60">
                  <ReadOnlyTiptapEditor content={editorContent} />
                </div>
                <div className="absolute bottom-0 left-0 w-full h-10 bg-gradient-to-b from-transparent to-inherit pointer-events-none rounded-b-lg" />
                <button
                  onClick={() => setCollapsed(false)}
                  className="expand-button"
                >
                  Show more <FiChevronDown className="w-3 h-3" />
                </button>
              </>
            ) : (
              <>
                <ReadOnlyTiptapEditor content={editorContent} />
                {isLong && (
                  <button
                    onClick={() => setCollapsed(true)}
                    className="expand-button"
                  >
                    Show less <FiChevronUp className="w-3 h-3" />
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </MessagePrimitive.Root>
  )
}

/**
 * Input component with character limit and keyboard shortcuts
 */
const CustomComposer: React.FC<{
  inputValue: string
  setInputValue: (value: string) => void
}> = ({ inputValue, setInputValue }) => {
  const [isOverLimit, setIsOverLimit] = useState(false)
  const sendButtonRef = useRef<HTMLButtonElement>(null)

  useEffect(() => {
    setIsOverLimit(inputValue.length > CONTEXT_CONFIG.MAX_INPUT_CHARS)
  }, [inputValue])

  const remainingChars = CONTEXT_CONFIG.MAX_INPUT_CHARS - inputValue.length

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      // Trigger send if not over limit and has content
      if (!isOverLimit && inputValue.trim()) {
        sendButtonRef.current?.click()
      }
    }
    // Shift+Enter creates a new line (default behavior when not prevented)
  }

  // Cross-platform shortcut text
  const shortcutText = 'Enter to send'

  return (
    <ComposerPrimitive.Root className="relative flex flex-col space-y-3">
      {/* Input container with professional styling */}
      <div className="relative">
        <ComposerPrimitive.Input
          placeholder="Message AI Assistant..."
          className={`composer-input ${isOverLimit ? 'composer-input-error' : ''}`}
          rows={1}
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          data-composer-input
        />

        {/* Send button positioned outside and properly centered */}
        <ComposerPrimitive.Send
          ref={sendButtonRef}
          disabled={isOverLimit || !inputValue.trim()}
          className="send-button"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
            />
          </svg>
        </ComposerPrimitive.Send>
      </div>

      {/* Keyboard shortcut hint and character counter */}
      <div className="flex justify-between items-center text-xs px-1">
        <div className="text-gray-600 font-medium">
          Press {shortcutText}, Shift+Enter for new line
        </div>
        <div
          className={
            isOverLimit ? 'text-red-600 font-semibold' : 'text-gray-500'
          }
        >
          {isOverLimit
            ? `${Math.abs(remainingChars)} characters over limit`
            : `${remainingChars} remaining`}
        </div>
      </div>

      {/* Error message for over limit */}
      {isOverLimit && (
        <div className="text-xs text-red-700 mt-1 p-3 bg-red-50 border border-red-200 rounded-xl">
          ⚠️ Message is too long. Please limit to{' '}
          {CONTEXT_CONFIG.MAX_INPUT_CHARS} characters.
        </div>
      )}
    </ComposerPrimitive.Root>
  )
}

/**
 * Context picker component for new conversations
 */
const ContextPicker: React.FC<{
  contextIds: string[]
  onContextChange: (contextIds: string[]) => void
  onClose: () => void
}> = ({ contextIds, onContextChange, onClose }) => {
  // TODO: Replace with real document selection from document store
  // This placeholder should be replaced with:
  // 1. API call to fetch available documents for the user
  // 2. Search/filter functionality for large document collections
  // 3. Document preview/summary for selection
  // 4. Recent/favorite document suggestions
  const availableDocuments = [
    { id: 'doc1', title: 'Document 1' },
    { id: 'doc2', title: 'Document 2' },
    { id: 'doc3', title: 'Document 3' },
  ]

  const handleToggleDocument = (docId: string) => {
    if (contextIds.includes(docId)) {
      onContextChange(contextIds.filter(id => id !== docId))
    } else {
      onContextChange([...contextIds, docId])
    }
  }

  return (
    <div className="border-b bg-yellow-50 p-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium">Select context documents</h3>
        <button
          onClick={onClose}
          className="text-xs text-gray-500 hover:text-gray-700"
        >
          Skip
        </button>
      </div>
      <div className="space-y-2">
        {availableDocuments.map(doc => (
          <label key={doc.id} className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={contextIds.includes(doc.id)}
              onChange={() => handleToggleDocument(doc.id)}
              className="rounded"
            />
            {doc.title}
          </label>
        ))}
      </div>
      <button
        onClick={onClose}
        className="mt-3 text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
      >
        Continue with selected documents ({contextIds.length})
      </button>
    </div>
  )
}

/**
 * Main ChatV2 component using assistant-ui
 */
export default function ChatV2({
  conversationId,
  contextIds = [],
  settings = {},
  onMessageFinish,
  showContextPicker = false,
  onContextChange,
}: ChatV2Props) {
  // State management (must be called before any early returns)
  const [isAtBottom, setIsAtBottom] = useState(true)
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)
  const [selectedContextIds, setSelectedContextIds] =
    useState<string[]>(contextIds)
  const [showingContextPicker, setShowingContextPicker] =
    useState(showContextPicker)
  const [inputValue, setInputValue] = useState('')
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  // Set up assistant runtime
  const runtime = useChatRuntime({
    api: '/api/aipane/assistant',
    body: {
      conversationId,
      metadata:
        selectedContextIds.length > 0
          ? { contextIds: selectedContextIds }
          : undefined,
      settings,
    },
    onFinish: onMessageFinish,
  })

  // TODO: Add input clearing on message send completion
  // The current useChatRuntime from @assistant-ui/react-ai-sdk doesn't expose events
  // This could be implemented with a custom hook or by monitoring runtime state changes

  // Scroll handling
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const { scrollTop, scrollHeight, clientHeight } = container
    const isNearBottom = scrollHeight - scrollTop - clientHeight <= 50

    setIsAtBottom(isNearBottom)
    setShowScrollToBottom(!isNearBottom && scrollTop > 200)
  }, [])

  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      })
    }
  }, [])

  // Auto-scroll on new messages
  useEffect(() => {
    if (isAtBottom) {
      setTimeout(() => {
        scrollToBottom()
      }, 100)
    }
  }, [isAtBottom, scrollToBottom])

  const handleContextChange = (newContextIds: string[]) => {
    setSelectedContextIds(newContextIds)
    onContextChange?.(newContextIds)
  }

  const handleCloseContextPicker = () => {
    setShowingContextPicker(false)
  }

  // Check feature flag after hooks are initialized
  if (!isFeatureEnabled('ENABLE_CHAT_V2')) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Chat V2 Not Available
          </h3>
          <p className="text-gray-500">This feature is currently disabled.</p>
        </div>
      </div>
    )
  }

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="flex flex-col h-full bg-gradient-to-b from-gray-50 to-white">
        {/* Context picker for new conversations */}
        {showingContextPicker && (
          <ContextPicker
            contextIds={selectedContextIds}
            onContextChange={handleContextChange}
            onClose={handleCloseContextPicker}
          />
        )}

        {/* Main chat thread */}
        <div className="flex-1 min-h-0 relative">
          <div
            ref={scrollContainerRef}
            className="h-full overflow-y-auto chat-thread p-6 bg-transparent"
            onScroll={handleScroll}
          >
            <ThreadPrimitive.Root className="h-full min-h-0">
              <ThreadPrimitive.Messages
                components={{ Message: CustomMessage }}
              />
            </ThreadPrimitive.Root>
          </div>

          {/* Scroll to bottom button */}
          {showScrollToBottom && (
            <button
              onClick={scrollToBottom}
              className="absolute bottom-6 right-6 bg-gray-900 hover:bg-black text-white rounded-full p-3 shadow-xl border-2 border-gray-700 hover:shadow-2xl transition-all duration-200 z-10 hover:scale-105 active:scale-95"
              aria-label="Scroll to bottom"
            >
              <FiArrowDown className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Input composer with character limit */}
        <div className="border-t-2 border-gray-200 bg-white/80 backdrop-blur-sm p-6">
          <CustomComposer
            inputValue={inputValue}
            setInputValue={setInputValue}
          />
        </div>
      </div>
    </AssistantRuntimeProvider>
  )
}

export {
  CustomMessage as _Test_CustomMessage,
  CustomComposer as _Test_CustomComposer,
}
